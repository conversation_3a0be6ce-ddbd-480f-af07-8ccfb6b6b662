{"cli": {"version": ">= 16.0.0", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}, "env": {"NODE_ENV": "development", "EXPO_USE_METRO_WORKSPACE_ROOT": "1", "EAS_SKIP_AUTO_FINGERPRINT": "1"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "env": {"NODE_ENV": "production", "EAS_BUILD": "true", "EXPO_USE_METRO_WORKSPACE_ROOT": "1", "EAS_SKIP_AUTO_FINGERPRINT": "1", "METRO_CACHE": "0", "METRO_CONFIG": "./metro.config.eas.js"}}, "production": {"android": {"buildType": "app-bundle", "gradleCommand": ":app:bundleRelease"}, "env": {"NODE_ENV": "production", "EAS_BUILD": "true", "EXPO_USE_METRO_WORKSPACE_ROOT": "1", "METRO_CONFIG": "./metro.config.eas.js"}}}, "submit": {"production": {"android": {"serviceAccountKeyPath": "./google-service-account.json", "track": "internal"}}}}